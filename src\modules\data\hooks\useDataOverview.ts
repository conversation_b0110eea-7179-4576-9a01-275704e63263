import { useQuery } from '@tanstack/react-query';
import type { DataOverviewStats } from '../types';

/**
 * Query keys cho data overview
 */
export const DATA_OVERVIEW_QUERY_KEYS = {
  all: ['data', 'overview'] as const,
  stats: () => [...DATA_OVERVIEW_QUERY_KEYS.all, 'stats'] as const,
};

/**
 * Mock service để lấy data overview stats
 * TODO: Thay thế bằng API call thực tế
 */
const getDataOverviewStats = async (): Promise<DataOverviewStats> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    totalMediaFiles: 1250,
    totalKnowledgeFiles: 340,
    totalUrls: 89,
    totalVectorStores: 12,
    storageUsed: '2.4 GB',
    lastUpdated: new Date().toISOString(),
  };
};

/**
 * Hook để lấy thống kê tổng quan data
 */
export const useDataOverview = () => {
  return useQuery({
    queryKey: DATA_OVERVIEW_QUERY_KEYS.stats(),
    queryFn: getDataOverviewStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook để lấy data counts cho từng module
 */
export const useDataModuleCounts = () => {
  return useQuery({
    queryKey: [...DATA_OVERVIEW_QUERY_KEYS.all, 'module-counts'],
    queryFn: async () => {
      // TODO: Implement actual API calls
      return {
        media: 1250,
        knowledgeFiles: 340,
        urls: 89,
        vectorStores: 12,
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};
