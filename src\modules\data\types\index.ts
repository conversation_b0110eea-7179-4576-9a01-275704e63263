/**
 * Data module types
 */

// Overview statistics types
export interface DataOverviewStats {
  totalMediaFiles: number;
  totalKnowledgeFiles: number;
  totalUrls: number;
  totalVectorStores: number;
  storageUsed: string;
  lastUpdated: string;
}

// Quick action types
export interface QuickAction {
  title: string;
  description: string;
  icon: any; // Lucide icon component
  action: () => void;
}

// Data module info
export interface DataModuleInfo {
  id: string;
  title: string;
  description: string;
  icon: string;
  linkTo: string;
  count?: number;
  lastUpdated?: string;
}

// Export all types from sub-modules
export * from './product';
export * from '../media/types';
export * from '../knowledge-files/types';
export * from '../url/types';