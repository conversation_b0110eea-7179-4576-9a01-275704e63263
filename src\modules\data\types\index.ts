/**
 * Data module types
 */

// Overview statistics types
export interface DataOverviewStats {
  totalMediaFiles: number;
  totalKnowledgeFiles: number;
  totalUrls: number;
  totalVectorStores: number;
  storageUsed: string;
  lastUpdated: string;
}

// Storage information types
export interface StorageInfo {
  used: number; // GB used
  total: number; // GB total
  percentage: number; // percentage used
  usedFormatted: string; // "2.4 GB"
  totalFormatted: string; // "10 GB"
  remainingFormatted: string; // "7.6 GB"
}

// Storage plan types
export interface StoragePlan {
  id: string;
  name: string;
  storage: number; // GB
  price: number; // VND
  priceFormatted: string; // "99,000 ₫"
  features: string[];
  isPopular?: boolean;
  isCurrentPlan?: boolean;
}

// Quick action types
export interface QuickAction {
  title: string;
  description: string;
  icon: any; // Lucide icon component
  action: () => void;
}

// Data module info
export interface DataModuleInfo {
  id: string;
  title: string;
  description: string;
  icon: string;
  linkTo: string;
  count?: number;
  lastUpdated?: string;
}

// Export all types from sub-modules
export * from './product';
export * from '../media/types';
export * from '../knowledge-files/types';
export * from '../url/types';