import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { FileImage, FileText, Link, Database, Upload, Plus, BarChart3 } from 'lucide-react';

import { ModuleCard } from '@/modules/components/card';
import { Card, Typography } from '@/shared/components/common';
import { ResponsiveGrid } from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';
import { useDataOverview } from '../hooks/useDataOverview';

/**
 * Trang tổng quan quản lý dữ liệu
 */
const DataManagementPage: React.FC = () => {
  const { t } = useTranslation(['data']);
  const navigate = useNavigate();

  // Fetch data overview từ API
  const { data: overviewData, isLoading: isOverviewLoading } = useDataOverview();

  // Overview statistics với data từ API hoặc fallback
  const overviewStats: OverviewCardProps[] = useMemo(() => [
    {
      title: t('data:overview.stats.totalMedia', 'Tổng Media Files'),
      value: overviewData?.totalMediaFiles || 1250,
      description: t('data:overview.stats.mediaDescription', '+15 files mới'),
      icon: FileImage,
      color: 'blue',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalKnowledge', 'File Tri Thức'),
      value: overviewData?.totalKnowledgeFiles || 340,
      description: t('data:overview.stats.knowledgeDescription', '+8 files mới'),
      icon: FileText,
      color: 'green',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalUrls', 'Tổng URLs'),
      value: overviewData?.totalUrls || 89,
      description: t('data:overview.stats.urlDescription', '+3 URLs mới'),
      icon: Link,
      color: 'orange',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalVectorStores', 'Vector Stores'),
      value: overviewData?.totalVectorStores || 12,
      description: overviewData?.storageUsed || t('data:overview.stats.vectorDescription', '2.4 GB sử dụng'),
      icon: Database,
      color: 'purple',
      isLoading: isOverviewLoading,
    },
  ], [t, overviewData, isOverviewLoading]);

  // Quick actions data
  const quickActions = useMemo(() => [
    {
      title: t('data:quickActions.uploadMedia', 'Upload Media'),
      description: t('data:quickActions.uploadMediaDesc', 'Tải lên hình ảnh, video, tài liệu'),
      icon: Upload,
      action: () => navigate('/data/media/upload'),
    },
    {
      title: t('data:quickActions.addKnowledge', 'Thêm File Tri Thức'),
      description: t('data:quickActions.addKnowledgeDesc', 'Import file cho AI training'),
      icon: Plus,
      action: () => navigate('/data/knowledge-files/create'),
    },
    {
      title: t('data:quickActions.addUrl', 'Thêm URL'),
      description: t('data:quickActions.addUrlDesc', 'Crawl nội dung từ website'),
      icon: Link,
      action: () => navigate('/data/url/create'),
    },
    {
      title: t('data:quickActions.analytics', 'Thống Kê'),
      description: t('data:quickActions.analyticsDesc', 'Xem báo cáo chi tiết'),
      icon: BarChart3,
      action: () => navigate('/data/analytics'),
    },
  ], [t, navigate]);

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Overview Statistics */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={isOverviewLoading}
        skeletonCount={4}
      />

      {/* Quick Actions */}
      <Card className="p-6">
        <Typography variant="h5" className="mb-4 font-semibold">
          {t('data:quickActions.title', 'Thao Tác Nhanh')}
        </Typography>
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
          gap={4}
        >
          {quickActions.map((action, index) => (
            <Card
              key={index}
              className="p-4 cursor-pointer hover:shadow-md transition-shadow border-dashed border-2 border-muted-foreground/20 hover:border-primary/50"
              onClick={action.action}
            >
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-primary/10">
                  <action.icon className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <Typography variant="subtitle2" className="font-medium truncate">
                    {action.title}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {action.description}
                  </Typography>
                </div>
              </div>
            </Card>
          ))}
        </ResponsiveGrid>
      </Card>

      {/* Module Navigation */}
      <Card className="p-6">
        <Typography variant="h5" className="mb-4 font-semibold">
          {t('data:modules.title', 'Quản Lý Dữ Liệu')}
        </Typography>
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
          gap={6}
        >
          {/* Media Card */}
          <ModuleCard
            title={t('data:media.title', 'Thư viện Media')}
            description={t(
              'data:media.description',
              'Quản lý các tệp tin media như hình ảnh, video, âm thanh và tài liệu.'
            )}
            icon="file-media"
            linkTo="/data/media"
          />

          {/* Knowledge Files Card */}
          <ModuleCard
            title={t('data:knowledgeFiles.title', 'File tri thức')}
            description={t(
              'data:knowledgeFiles.description',
              'Quản lý các tệp tin tri thức được sử dụng cho AI và vector store.'
            )}
            icon="file-text"
            linkTo="/data/knowledge-files"
          />

          {/* URL Card */}
          <ModuleCard
            title={t('data:url.title', 'Quản lý URL')}
            description={t(
              'data:url.description',
              'Quản lý các URL và tài nguyên web được sử dụng trong hệ thống.'
            )}
            icon="link"
            linkTo="/data/url"
          />

          {/* Vector Store Card */}
          <ModuleCard
            title={t('data:vectorStore.title', 'Vector Store')}
            description={t(
              'data:vectorStore.description',
              'Quản lý các vector store và embedding cho các ứng dụng AI và tìm kiếm ngữ nghĩa.'
            )}
            icon="server"
            linkTo="/data/vector-store"
          />
        </ResponsiveGrid>
      </Card>
    </div>
  );
};

export default DataManagementPage;
